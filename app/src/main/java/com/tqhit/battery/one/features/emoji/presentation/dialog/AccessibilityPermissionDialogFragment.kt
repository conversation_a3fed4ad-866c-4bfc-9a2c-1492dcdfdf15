package com.tqhit.battery.one.features.emoji.presentation.dialog

import android.app.Dialog
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.DialogAccessibilityPermissionBinding
import com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiOverlayPermissionManager
import com.tqhit.battery.one.utils.BatteryLogger

/**
 * Enhanced accessibility permission dialog that follows Material 3 design guidelines.
 * Provides clear explanations and proper action buttons for accessibility permission requests.
 * 
 * This dialog is specifically designed for the emoji overlay feature and includes:
 * - Clear explanation of why accessibility permission is needed
 * - Material 3 design with proper styling and spacing
 * - Three action buttons: Grant Permission, Cancel, Learn More
 * - Comprehensive logging for user interactions
 * - Proper lifecycle management and configuration change handling
 */
class AccessibilityPermissionDialogFragment : DialogFragment() {

    companion object {
        private const val TAG = "AccessibilityPermissionDialog"
        private const val DIALOG_TAG = "AccessibilityPermissionDialog"

        // Keys for state restoration
        private const val KEY_DIALOG_SHOWN_TIME = "dialog_shown_time"
        private const val KEY_USER_NAVIGATED_TO_SETTINGS = "user_navigated_to_settings"

        /**
         * Creates a new instance of the dialog
         */
        fun newInstance(): AccessibilityPermissionDialogFragment {
            return AccessibilityPermissionDialogFragment()
        }
    }

    private var _binding: DialogAccessibilityPermissionBinding? = null
    private val binding get() = _binding!!

    // Callback interfaces for handling user actions
    var onGrantPermissionClicked: (() -> Unit)? = null
    var onCancelClicked: (() -> Unit)? = null
    var onLearnMoreClicked: (() -> Unit)? = null

    // State tracking for edge case handling
    private var dialogShownTime: Long = 0L
    private var userNavigatedToSettings: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.i(TAG, "EMOJI_PERMISSION: Accessibility permission dialog created")
        Log.d(TAG, "EMOJI_PERMISSION: Dialog lifecycle - onCreate called")

        // Restore state if available (app kill/restore scenario)
        savedInstanceState?.let { bundle ->
            dialogShownTime = bundle.getLong(KEY_DIALOG_SHOWN_TIME, System.currentTimeMillis())
            userNavigatedToSettings = bundle.getBoolean(KEY_USER_NAVIGATED_TO_SETTINGS, false)
            Log.d(TAG, "EMOJI_PERMISSION: State restored - dialogShownTime: $dialogShownTime, userNavigatedToSettings: $userNavigatedToSettings")
        } ?: run {
            dialogShownTime = System.currentTimeMillis()
            Log.d(TAG, "EMOJI_PERMISSION: New dialog instance - dialogShownTime: $dialogShownTime")
        }

        // Set dialog style for Material 3 appearance
        setStyle(STYLE_NO_TITLE, R.style.Theme_Material3_Dialog)

        // Log dialog configuration
        Log.d(TAG, "EMOJI_PERMISSION: Dialog style set to Material 3")
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        // Save state for app kill/restore scenarios
        outState.putLong(KEY_DIALOG_SHOWN_TIME, dialogShownTime)
        outState.putBoolean(KEY_USER_NAVIGATED_TO_SETTINGS, userNavigatedToSettings)

        Log.d(TAG, "EMOJI_PERMISSION: State saved - dialogShownTime: $dialogShownTime, userNavigatedToSettings: $userNavigatedToSettings")
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogAccessibilityPermissionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        Log.d(TAG, "EMOJI_PERMISSION: Setting up accessibility permission dialog UI")
        
        setupUI()
        setupClickListeners()
        
        // Log dialog display
        Log.i(TAG, "EMOJI_PERMISSION: Accessibility permission dialog displayed to user")
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        
        // Configure dialog window properties
        dialog.window?.let { window ->
            window.setLayout(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.WRAP_CONTENT
            )
            window.setBackgroundDrawableResource(android.R.color.transparent)
        }
        
        // Prevent dismissal on outside touch for better UX
        dialog.setCanceledOnTouchOutside(false)
        
        return dialog
    }

    /**
     * Sets up the UI components and styling
     */
    private fun setupUI() {
        try {
            // The layout already contains all the necessary text and styling
            // Additional UI setup can be added here if needed
            
            Log.d(TAG, "EMOJI_PERMISSION: Dialog UI setup completed")
        } catch (exception: Exception) {
            Log.e(TAG, "EMOJI_PERMISSION: Error setting up dialog UI", exception)
        }
    }

    /**
     * Sets up click listeners for all action buttons
     */
    private fun setupClickListeners() {
        try {
            // Grant Permission button
            binding.grantPermissionButton.setOnClickListener {
                Log.i(TAG, "EMOJI_PERMISSION: User clicked Grant Permission button")
                handleGrantPermissionClick()
            }

            // Cancel button
            binding.cancelButton.setOnClickListener {
                Log.i(TAG, "EMOJI_PERMISSION: User clicked Cancel button")
                handleCancelClick()
            }

            // Learn More button
            binding.learnMoreButton.setOnClickListener {
                Log.i(TAG, "EMOJI_PERMISSION: User clicked Learn More button")
                handleLearnMoreClick()
            }

            // Close button (X)
            binding.closeButton.setOnClickListener {
                Log.i(TAG, "EMOJI_PERMISSION: User clicked Close button")
                handleCancelClick()
            }

            Log.d(TAG, "EMOJI_PERMISSION: Click listeners setup completed")
        } catch (exception: Exception) {
            Log.e(TAG, "EMOJI_PERMISSION: Error setting up click listeners", exception)
        }
    }

    /**
     * Handles Grant Permission button click
     */
    private fun handleGrantPermissionClick() {
        try {
            Log.i(TAG, "EMOJI_PERMISSION: Processing grant permission request")

            // Mark that user is navigating to settings
            userNavigatedToSettings = true

            // Dismiss dialog first
            dismiss()

            // Navigate to accessibility settings
            openAccessibilitySettings()

            // Notify callback
            onGrantPermissionClicked?.invoke()

        } catch (exception: Exception) {
            Log.e(TAG, "EMOJI_PERMISSION: Error handling grant permission click", exception)
        }
    }

    /**
     * Handles Cancel button click
     */
    private fun handleCancelClick() {
        try {
            Log.i(TAG, "EMOJI_PERMISSION: User cancelled permission request")
            
            // Dismiss dialog
            dismiss()
            
            // Notify callback
            onCancelClicked?.invoke()
            
        } catch (exception: Exception) {
            Log.e(TAG, "EMOJI_PERMISSION: Error handling cancel click", exception)
        }
    }

    /**
     * Handles Learn More button click
     */
    private fun handleLearnMoreClick() {
        try {
            Log.i(TAG, "EMOJI_PERMISSION: User clicked Learn More")
            
            // Open help/documentation URL or show additional info
            // For now, we'll just log and potentially show a toast or navigate to help
            
            // Notify callback
            onLearnMoreClicked?.invoke()
            
        } catch (exception: Exception) {
            Log.e(TAG, "EMOJI_PERMISSION: Error handling learn more click", exception)
        }
    }

    /**
     * Opens the accessibility settings page for the user to enable the service
     * Handles various edge cases and provides fallback options
     */
    private fun openAccessibilitySettings() {
        try {
            Log.i(TAG, "EMOJI_PERMISSION: Opening accessibility settings")

            // Check if activity is still valid before starting new activity
            val activity = activity
            if (activity == null || activity.isFinishing || activity.isDestroyed) {
                Log.w(TAG, "EMOJI_PERMISSION: Activity is null or finishing, cannot open settings")
                return
            }

            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }

            startActivity(intent)
            Log.i(TAG, "EMOJI_PERMISSION: Successfully navigated to accessibility settings")

        } catch (securityException: SecurityException) {
            Log.e(TAG, "EMOJI_PERMISSION: SecurityException opening accessibility settings", securityException)
            tryFallbackSettings()
        } catch (exception: Exception) {
            Log.e(TAG, "EMOJI_PERMISSION: Error opening accessibility settings", exception)
            tryFallbackSettings()
        }
    }

    /**
     * Tries fallback settings options when primary accessibility settings fail
     */
    private fun tryFallbackSettings() {
        try {
            val activity = activity
            if (activity == null || activity.isFinishing || activity.isDestroyed) {
                Log.w(TAG, "EMOJI_PERMISSION: Activity is null or finishing, cannot open fallback settings")
                return
            }

            val fallbackIntent = Intent(Settings.ACTION_SETTINGS).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            startActivity(fallbackIntent)
            Log.i(TAG, "EMOJI_PERMISSION: Opened general settings as fallback")

        } catch (fallbackException: Exception) {
            Log.e(TAG, "EMOJI_PERMISSION: Error opening fallback settings", fallbackException)
            // At this point, we've exhausted all options
            Log.e(TAG, "EMOJI_PERMISSION: All settings navigation options failed")
        }
    }

    override fun onDismiss(dialog: android.content.DialogInterface) {
        super.onDismiss(dialog)
        Log.i(TAG, "EMOJI_PERMISSION: Dialog dismissed by user")
    }

    override fun onCancel(dialog: android.content.DialogInterface) {
        super.onCancel(dialog)
        Log.i(TAG, "EMOJI_PERMISSION: Dialog cancelled by user (back button or outside touch)")
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        Log.d(TAG, "EMOJI_PERMISSION: Dialog view destroyed")
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "EMOJI_PERMISSION: Dialog destroyed")
    }
}
