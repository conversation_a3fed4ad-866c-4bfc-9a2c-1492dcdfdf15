<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/white_block"
    android:padding="24dp"
    android:elevation="8dp">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <!-- Icon -->
        <ImageView
            android:id="@+id/permissionIcon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_accessibility"
            android:layout_marginEnd="12dp"
            android:contentDescription="@string/accessibility_permission_icon_description" />

        <!-- Title -->
        <TextView
            android:id="@+id/titleText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/accessibility_service_usage_title"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="?attr/black"
            android:fontFamily="sans-serif-medium" />

        <!-- Close Button -->
        <ImageButton
            android:id="@+id/closeButton"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_close"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/close_dialog" />

    </LinearLayout>

    <!-- Subtitle -->
    <TextView
        android:id="@+id/subtitleText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/accessibility_service_usage_subtitle"
        android:textSize="16sp"
        android:textColor="?attr/black"
        android:layout_marginBottom="20dp"
        android:lineSpacingExtra="2dp" />

    <!-- Permission Explanation Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/grey_block"
        android:padding="16dp"
        android:layout_marginBottom="20dp">

        <TextView
            android:id="@+id/explanationTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/accessibility_permission_explanation_title"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="?attr/black"
            android:layout_marginBottom="12dp" />

        <!-- Permission Item 1 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="•"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/accessibility_permission_item_1"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:lineSpacingExtra="2dp" />

        </LinearLayout>

        <!-- Permission Item 2 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="•"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/accessibility_permission_item_2"
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:lineSpacingExtra="2dp" />

        </LinearLayout>

        <!-- Privacy Note -->
        <TextView
            android:id="@+id/privacyNote"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/accessibility_privacy_note"
            android:textSize="12sp"
            android:textColor="?attr/grey"
            android:textStyle="italic"
            android:layout_marginTop="8dp"
            android:lineSpacingExtra="1dp" />

    </LinearLayout>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <!-- Learn More Button -->
        <Button
            android:id="@+id/learnMoreButton"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/learn_more"
            android:textColor="?attr/colorPrimary"
            android:layout_marginEnd="8dp"
            android:minWidth="0dp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

        <!-- Cancel Button -->
        <Button
            android:id="@+id/cancelButton"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/cancel"
            android:textColor="?attr/grey"
            android:layout_marginEnd="8dp"
            android:minWidth="0dp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

        <!-- Grant Permission Button -->
        <Button
            android:id="@+id/grantPermissionButton"
            style="@style/Widget.Material3.Button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/grant_permission"
            android:backgroundTint="?attr/colorPrimary"
            android:textColor="@android:color/white"
            android:minWidth="0dp"
            android:paddingStart="24dp"
            android:paddingEnd="24dp" />

    </LinearLayout>

</LinearLayout>
